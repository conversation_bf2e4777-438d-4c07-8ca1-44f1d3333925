import { exec } from 'child_process'
import { promisify } from 'util'
import path from 'path'

const execAsync = promisify(exec)

export interface DocumentContent {
  id: number
  title: string
  content: string
  htmlContent: string
}

// 文档文件映射
const DOCX_FILES = {
  1: '地底之谜揭晓.docx',
  2: 'EVERCALL新角色悠悠的上线预告.docx', 
  3: '内测招募页.docx'
}

/**
 * 使用 pandoc 将 docx 文件转换为 HTML
 */
async function convertDocxToHtml(docxPath: string): Promise<string> {
  try {
    const { stdout } = await execAsync(`pandoc "${docxPath}" -t html --wrap=none`)
    return stdout
  } catch (error) {
    console.error('Error converting docx to HTML:', error)
    return '<p>文档内容加载失败</p>'
  }
}

/**
 * 使用 pandoc 将 docx 文件转换为纯文本
 */
async function convertDocxToText(docxPath: string): Promise<string> {
  try {
    const { stdout } = await execAsync(`pandoc "${docxPath}" -t plain --wrap=none`)
    return stdout.trim()
  } catch (error) {
    console.error('Error converting docx to text:', error)
    return '文档内容加载失败'
  }
}

/**
 * 解析单个文档
 */
export async function parseDocument(id: number): Promise<DocumentContent | null> {
  const fileName = DOCX_FILES[id as keyof typeof DOCX_FILES]
  if (!fileName) {
    return null
  }

  const docxPath = path.join(process.cwd(), 'public', 'docx', fileName)
  
  try {
    const [htmlContent, textContent] = await Promise.all([
      convertDocxToHtml(docxPath),
      convertDocxToText(docxPath)
    ])

    // 从文件名提取标题
    const title = fileName.replace('.docx', '')

    return {
      id,
      title,
      content: textContent,
      htmlContent
    }
  } catch (error) {
    console.error(`Error parsing document ${id}:`, error)
    return null
  }
}

/**
 * 解析所有文档
 */
export async function parseAllDocuments(): Promise<DocumentContent[]> {
  const documentIds = Object.keys(DOCX_FILES).map(Number)
  const results = await Promise.allSettled(
    documentIds.map(id => parseDocument(id))
  )

  return results
    .filter((result): result is PromiseFulfilledResult<DocumentContent> => 
      result.status === 'fulfilled' && result.value !== null
    )
    .map(result => result.value)
}

/**
 * 获取文档内容的缓存版本（用于客户端）
 */
export function getDocumentContentSync(id: number): DocumentContent | null {
  // 这个函数将在服务端预处理时使用
  // 客户端将使用预处理的数据
  return null
}
