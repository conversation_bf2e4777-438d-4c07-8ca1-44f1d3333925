/* 新闻详情页面样式 */
.news-detail-content {
  color: #ffffff;
  line-height: 1.8;
}

.news-detail-content h1,
.news-detail-content h2,
.news-detail-content h3,
.news-detail-content h4,
.news-detail-content h5,
.news-detail-content h6 {
  color: #00d4ff;
  margin-top: 2rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.news-detail-content h1 {
  font-size: 2.5rem;
  margin-top: 0;
}

.news-detail-content h2 {
  font-size: 2rem;
}

.news-detail-content h3 {
  font-size: 1.5rem;
}

.news-detail-content p {
  margin-bottom: 1.5rem;
  color: #e5e7eb;
}

.news-detail-content strong {
  color: #ffffff;
  font-weight: 600;
}

.news-detail-content em {
  color: #00d4ff;
  font-style: italic;
}

.news-detail-content ul,
.news-detail-content ol {
  margin-bottom: 1.5rem;
  padding-left: 2rem;
}

.news-detail-content li {
  margin-bottom: 0.5rem;
  color: #e5e7eb;
}

.news-detail-content blockquote {
  border-left: 4px solid #00d4ff;
  padding-left: 1.5rem;
  margin: 2rem 0;
  font-style: italic;
  color: #d1d5db;
  background: rgba(0, 212, 255, 0.1);
  padding: 1rem 1.5rem;
  border-radius: 0.5rem;
}

.news-detail-content code {
  background: rgba(0, 212, 255, 0.2);
  color: #00d4ff;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', monospace;
}

.news-detail-content pre {
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 0.5rem;
  padding: 1.5rem;
  overflow-x: auto;
  margin: 1.5rem 0;
}

.news-detail-content pre code {
  background: none;
  padding: 0;
}

.news-detail-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 0.5rem;
  overflow: hidden;
}

.news-detail-content th,
.news-detail-content td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid rgba(0, 212, 255, 0.2);
}

.news-detail-content th {
  background: rgba(0, 212, 255, 0.2);
  color: #ffffff;
  font-weight: 600;
}

.news-detail-content td {
  color: #e5e7eb;
}

.news-detail-content a {
  color: #00d4ff;
  text-decoration: underline;
  transition: color 0.3s ease;
}

.news-detail-content a:hover {
  color: #ffffff;
}

.news-detail-content img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 2rem 0;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
}

.news-detail-content hr {
  border: none;
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(0, 212, 255, 0.5), transparent);
  margin: 3rem 0;
}

/* 滚动条样式 */
.news-detail-content::-webkit-scrollbar {
  width: 8px;
}

.news-detail-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}

.news-detail-content::-webkit-scrollbar-thumb {
  background: rgba(0, 212, 255, 0.5);
  border-radius: 4px;
}

.news-detail-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 212, 255, 0.7);
}
