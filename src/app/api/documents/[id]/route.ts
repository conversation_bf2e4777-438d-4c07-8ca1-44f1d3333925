import { NextRequest, NextResponse } from 'next/server'
import { parseDocument } from '@/utils/docxParser'

export const dynamic = 'force-dynamic'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)

    if (isNaN(id) || id < 1 || id > 3) {
      return NextResponse.json(
        { error: '无效的文档ID' },
        { status: 400 }
      )
    }

    const document = await parseDocument(id)

    if (!document) {
      return NextResponse.json(
        { error: '文档未找到' },
        { status: 404 }
      )
    }

    return NextResponse.json(document)
  } catch (error) {
    console.error('Error fetching document:', error)
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}
