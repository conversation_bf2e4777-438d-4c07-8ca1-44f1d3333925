'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Image from 'next/image'
import { ChevronRight, ArrowLeft, X } from 'lucide-react'
import '@/styles/news-detail.css'

// 文档内容接口
interface DocumentContent {
  id: number
  title: string
  content: string
  htmlContent: string
}

// 轮播横幅数据 - 基于UI图更新
const carouselBanners = [
  {
    id: 1,
    title: '地底之谜揭晓：神秘装置EVERCALL或将改写人类文明走向',
    image: '/images/news/change.jpg',
    url: '',
  },
  {
    id: 2,
    title: 'EVERCALL新角色悠悠的上线预告',
    image: '/images/news/role.jpg',
    url: '',
  },
  {
    id: 3,
    title: '内测招募页',
    image: '/images/news/recruitment.jpg',
    url: 'www.baidu.com',
  }
]

// 新闻数据 - 根据UI图更新
const newsData = [
  {
    id: 1,
    type: '新闻',
    date: '2025 // 08 / 01',
    title: '地底之谜揭晓：神秘装置EVERCALL或将改写人类文明走向',
    url: '',
  },
  {
    id: 2,
    type: '新闻',
    date: '2025 // 07 / 19',
    title: 'EVERCALL新角色悠悠的上线预告',
    url: '',
  },
  {
    id: 3,
    type: '新闻',
    date: '2025 // 07 / 17',
    title: '内测招募页',
    url: 'www.baidu.com',
  }
]

export default function ImprovedInformationSection() {
  const [currentBanner, setCurrentBanner] = useState(0) // 默认显示第1个（索引0）
  const [activeFilter, setActiveFilter] = useState('新闻')
  const [showDetail, setShowDetail] = useState(false)
  const [selectedNews, setSelectedNews] = useState<number | null>(null)
  const [documentContent, setDocumentContent] = useState<DocumentContent | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  const filters = ['新闻']

  // 自动轮播
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentBanner((prev) => (prev + 1) % carouselBanners.length)
    }, 5000)
    return () => clearInterval(interval)
  }, [])

  // 手动切换轮播
  const handleBannerClick = (index: number) => {
    setCurrentBanner(index)
  }

  // 获取文档内容
  const fetchDocumentContent = async (id: number) => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/documents/${id}`)
      if (response.ok) {
        const content = await response.json()
        setDocumentContent(content)
      } else {
        console.error('Failed to fetch document content')
        setDocumentContent(null)
      }
    } catch (error) {
      console.error('Error fetching document content:', error)
      setDocumentContent(null)
    } finally {
      setIsLoading(false)
    }
  }

  // 处理新闻点击
  const handleNewsClick = async (news: typeof newsData[0]) => {
    // 如果有URL且不为空，在新标签页打开
    if (news.url && news.url.trim() !== '') {
      window.open(news.url, '_blank')
      return
    }

    // 否则显示详情页面
    setSelectedNews(news.id)
    setShowDetail(true)
    await fetchDocumentContent(news.id)
  }

  // 处理轮播图点击
  const handleBannerNewsClick = async (banner: typeof carouselBanners[0]) => {
    // 如果有URL且不为空，在新标签页打开
    if (banner.url && banner.url.trim() !== '') {
      window.open(banner.url, '_blank')
      return
    }

    // 否则显示详情页面
    setSelectedNews(banner.id)
    setShowDetail(true)
    await fetchDocumentContent(banner.id)
  }

  // 关闭详情页面
  const closeDetail = () => {
    setShowDetail(false)
    setSelectedNews(null)
    setDocumentContent(null)
  }

  const filteredNews = newsData.filter(item => item.type === '新闻')

  // 计算轮播位置 - 适配两列布局
  const getTransformStyle = () => {
    const slideWidth = 100 // 每个slide占100%宽度
    const translateX = -currentBanner * slideWidth
    return {
      cursor: 'grab',
      transitionDuration: '500ms',
      transform: `translate3d(${translateX}%, 0px, 0px)`,
      transitionDelay: '0ms'
    }
  }

  // 计算滚动条位置 - 根据轮播图数量动态调整
  const getScrollbarStyle = () => {
    const totalWidth = 100 // 滚动条总宽度百分比
    const dragWidth = totalWidth / carouselBanners.length // 根据轮播图数量动态计算拖拽条宽度
    const position = currentBanner * dragWidth // 当前位置基于轮播图索引
    return {
      transform: `translate3d(${Math.round(position)}%, 0px, 0px)`,
      width: `${dragWidth}%`,
      transitionDuration: '300ms'
    }
  }

  return (
    <section
      className="relative w-full h-screen overflow-hidden bg-gradient-to-br from-slate-900 via-black/20 to-slate-800">
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent z-10" />
      <div className="absolute left-5 bottom-[10px] text-[100px] font-bold text-white/20 z-10]">
        EVERCALL NEWS
      </div>

      <div className="absolute inset-0 pl-0 pr-52 pt-20 pb-10 overflow-hidden z-50">
        <div className="relative w-full h-full">
          <div className="flex h-full">
            {/* 左侧新闻列表区域 - 根据UI图重构 */}
            <div className="w-1/5 min-w-80 flex flex-col pointer-events-auto p-5">
              {/* 新闻分类标签页 - 水平布局 */}
              <motion.div
                className="flex gap-1 mb-2 bg-black/20 backdrop-blur-sm border-b border-gray-600/100"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
              >
                {filters.map((filter, index) => (
                  <motion.button
                    key={filter}
                    className={`flex-1 px-1 py-1 text-sm font-medium transition-all duration-300 ${activeFilter === filter
                        ? 'bg-cyan-400 text-black shadow-lg'
                        : 'text-white/80 hover:text-white hover:bg-white/10'
                      }`}
                    onClick={() => setActiveFilter(filter)}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
                  >
                    {filter}
                  </motion.button>
                ))}
              </motion.div>

              {/* 新闻列表 - 根据UI图重新设计 */}
              <motion.div
                className="flex-1 overflow-y-auto"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
              >
                {filteredNews.map((news, index) => (
                  <motion.div
                    key={news.id}
                    className="group flex items-start gap-4 py-4 px-2 border-b border-gray-600/30 hover:bg-black/50 transition-all duration-300 cursor-pointer"
                    onClick={() => handleNewsClick(news)}
                    initial={{ opacity: 0, x: -30 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                    whileHover={{ x: 5 }}
                  >
                    {/* 左侧类型标签 */}
                    <div className={`flex-shrink-0 px-2 py-1 text-sm font-medium rounded font-bold text-cyan-500`}>
                      {news.type}
                    </div>

                    {/* 右侧内容区域 */}
                    <div className="flex-1 min-w-0">
                      {/* 日期 */}
                      <div className="text-white/60 text-[9px] font-mono">
                        {news.date}
                      </div>
                      {/* 标题 */}
                      <div className="text-white text-sm group-hover:text-cyan-400 transition-colors duration-300 line-clamp-2">
                        {news.title}
                      </div>
                    </div>
                  </motion.div>
                ))}

                {/* READ MORE 按钮 */}
                {/*<motion.div
                  className="pt-4"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 1 }}
                >
                  <motion.a
                    className="group flex items-center gap-2 px-3 py-1 bg-gray-700/50 hover:bg-gray-600/50 transition-all duration-300 text-white/80 hover:text-white text-sm w-[110px]"
                    target="_blank"
                    href="/news"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <span className="font-medium text-[12px]">READ MORE</span>
                    <div className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300">
                      <ChevronRight className="w-full h-full" />
                    </div>
                  </motion.a>
                </motion.div>*/}
              </motion.div>
            </div>

            {/* 右侧主轮播横幅区域 - 较宽 */}
            <div className="flex-1 flex flex-col pointer-events-auto">
              {/* 主轮播横幅 */}
              <div className="relative -mt-20 -mr-60 overflow-hidden aspect-[16/8]">
                <div className="relative w-full h-full">
                  <div
                    className="flex transition-transform duration-500 ease-out h-full"
                    style={getTransformStyle()}
                  >
                    {carouselBanners.map((banner, index) => {
                      const isActive = index === currentBanner

                      return (
                        <motion.div
                          key={banner.id}
                          className="flex-shrink-0 cursor-pointer h-full"
                          style={{ width: '100%' }}
                          onClick={() => handleBannerClick(index)}
                          whileHover={{ scale: isActive ? 1.02 : 1 }}
                          transition={{ duration: 0.3 }}
                        >
                          <div
                            className="block w-full h-full group cursor-pointer"
                            onClick={() => handleBannerNewsClick(banner)}
                          >
                            <div className="relative w-full h-full overflow-hidden shadow-2xl">
                              <Image
                                src={banner.image}
                                alt={banner.title}
                                fill
                                className="object-cover transition-transform duration-700 group-hover:scale-105"
                                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 70vw, 800px"
                                priority={isActive}
                              />
                              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                              <div className="absolute bottom-8 left-8 text-white">
                                <h3 className="text-3xl font-bold mb-2 drop-shadow-lg">{banner.title}</h3>
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      )
                    })}
                  </div>
                </div>
              </div>

              {/* 轮播滚动条 */}
              <div className="">
                <div className="relative w-full h-1 bg-white/20  overflow-hidden">
                  <motion.div
                    className="absolute top-0 left-0 h-full bg-ak-primary/50  shadow-lg shadow-ak-primary/50"
                    style={getScrollbarStyle()}
                    transition={{ duration: 0.3 }}
                  />
                </div>
              </div>

              {/* 轮播指示器 */}
              <div className="flex justify-center gap-2 mt-4">
                {carouselBanners.map((_, index) => (
                  <button
                    key={index}
                    className={`w-2 h-2 transition-all duration-300 ${index === currentBanner
                        ? 'bg-ak-primary shadow-lg shadow-ak-primary/50'
                        : 'bg-white/30 hover:bg-white/50'
                      }`}
                    onClick={() => handleBannerClick(index)}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 装饰背景元素 */}
      <div className="absolute top-1/4 right-1/4 w-64 h-64 bg-gradient-radial from-ak-primary/10 to-transparent rounded-full animate-pulse" />
      <div className="absolute bottom-1/4 left-1/4 w-48 h-48 bg-gradient-radial from-blue-500/10 to-transparent rounded-full animate-pulse delay-1000" />

      {/* 详情页面模态框 */}
      <AnimatePresence>
        {showDetail && (
          <motion.div
            className="fixed inset-0 z-[100] bg-black/80 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={closeDetail}
          >
            <motion.div
              className="absolute inset-4 bg-gradient-to-br from-slate-900 via-black/90 to-slate-800 rounded-lg overflow-hidden"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* 详情页面头部 */}
              <div className="flex items-center justify-between p-6 border-b border-gray-600/30">
                <div className="flex items-center gap-4">
                  <button
                    onClick={closeDetail}
                    className="flex items-center gap-2 text-white/80 hover:text-white transition-colors"
                  >
                    <ArrowLeft className="w-5 h-5" />
                    <span>返回</span>
                  </button>
                  <div className="w-px h-6 bg-gray-600/30" />
                  <h2 className="text-xl font-bold text-white">
                    {documentContent?.title || '加载中...'}
                  </h2>
                </div>
                <button
                  onClick={closeDetail}
                  className="text-white/80 hover:text-white transition-colors"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>

              {/* 详情页面内容 */}
              <div className="flex-1 overflow-y-auto p-6">
                {isLoading ? (
                  <div className="flex items-center justify-center h-64">
                    <div className="text-white/60">加载中...</div>
                  </div>
                ) : documentContent ? (
                  <div className="max-w-4xl mx-auto">
                    <div
                      className="news-detail-content"
                      dangerouslySetInnerHTML={{ __html: documentContent.htmlContent }}
                    />
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-64">
                    <div className="text-white/60">内容加载失败</div>
                  </div>
                )}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

    </section>
  )
}
