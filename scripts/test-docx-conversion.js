const { exec } = require('child_process');
const path = require('path');

// 测试 docx 文件转换
async function testDocxConversion() {
  const docxFiles = [
    '地底之谜揭晓.docx',
    'EVERCALL新角色悠悠的上线预告.docx',
    '内测招募页.docx'
  ];

  for (const fileName of docxFiles) {
    const docxPath = path.join(__dirname, '..', 'public', 'docx', fileName);
    console.log(`\n测试文件: ${fileName}`);
    console.log(`文件路径: ${docxPath}`);
    
    try {
      // 测试转换为 HTML
      const htmlCommand = `pandoc "${docxPath}" -t html --wrap=none`;
      exec(htmlCommand, (error, stdout, stderr) => {
        if (error) {
          console.error(`HTML转换错误: ${error}`);
          return;
        }
        if (stderr) {
          console.error(`HTML转换警告: ${stderr}`);
        }
        console.log(`HTML转换成功，内容长度: ${stdout.length} 字符`);
        console.log(`HTML预览: ${stdout.substring(0, 200)}...`);
      });

      // 测试转换为纯文本
      const textCommand = `pandoc "${docxPath}" -t plain --wrap=none`;
      exec(textCommand, (error, stdout, stderr) => {
        if (error) {
          console.error(`文本转换错误: ${error}`);
          return;
        }
        if (stderr) {
          console.error(`文本转换警告: ${stderr}`);
        }
        console.log(`文本转换成功，内容长度: ${stdout.length} 字符`);
        console.log(`文本预览: ${stdout.substring(0, 200)}...`);
      });
    } catch (error) {
      console.error(`处理文件 ${fileName} 时出错:`, error);
    }
  }
}

testDocxConversion();
